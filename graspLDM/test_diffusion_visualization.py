#!/usr/bin/env python3
"""
测试 Diffusion 过程可视化功能

这个脚本演示了如何使用新增的 diffusion 过程可视化功能。
"""

import os
import sys
import numpy as np
import torch

# 添加路径以便导入模块
sys.path.append(os.getcwd())

def test_diffusion_visualization():
    """测试 diffusion 可视化功能"""
    
    print("🧪 测试 Diffusion 过程可视化功能")
    print("=" * 50)
    
    # 检查是否有模型路径
    exp_path = "checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k"
    
    if not os.path.exists(exp_path):
        print(f"❌ 模型路径不存在: {exp_path}")
        print("请确保已下载模型检查点，或修改 exp_path 变量")
        return False
    
    try:
        # 导入推理类
        from tools.simple_inference import SimpleGraspLDMInference
        
        # 初始化推理引擎
        print("🚀 初始化推理引擎...")
        inference = SimpleGraspLDMInference(
            exp_path=exp_path,
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            num_inference_steps=20  # 较少步数用于快速测试
        )
        
        # 生成测试点云（简单球体）
        print("📊 生成测试点云...")
        n_points = 1000
        theta = np.random.uniform(0, 2*np.pi, n_points)
        phi = np.random.uniform(0, np.pi, n_points)
        r = 0.15  # 15cm 半径
        
        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)
        
        test_pc = np.column_stack([x, y, z])
        print(f"✓ 创建测试点云: {test_pc.shape}")
        
        # 生成抓取并获取中间步骤
        print("🤖 生成抓取（包含中间步骤）...")
        results = inference.infer_from_pointcloud(
            pointcloud=test_pc,
            num_grasps=8,  # 较少抓取数量用于快速测试
            return_intermediate=True,  # 关键：启用中间步骤
            visualize=False
        )
        
        # 检查结果
        print(f"✓ 生成了 {results['grasps'].shape[0]} 个抓取")
        
        if results.get('all_steps_grasps'):
            print(f"✓ 捕获了 {len(results['all_steps_grasps'])} 个中间步骤")
            
            # 测试1：便捷可视化方法
            print("\n🎯 测试便捷可视化方法...")
            success1 = inference.visualize_diffusion_process(
                results,
                max_steps=5,
                save_base_path="test_diffusion"
            )
            print(f"便捷方法结果: {'✅' if success1 else '❌'}")
            
            # 测试2：帧序列保存
            print("\n📽️ 测试帧序列保存...")
            success2 = inference.save_diffusion_evolution(
                results,
                "test_frames.glb",
                output_format="frames",
                max_steps=4
            )
            print(f"帧序列保存结果: {'✅' if success2 else '❌'}")
            
            # 测试3：组合场景保存
            print("\n🎨 测试组合场景保存...")
            success3 = inference.save_diffusion_evolution(
                results,
                "test_combined.glb",
                output_format="combined",
                max_steps=6
            )
            print(f"组合场景保存结果: {'✅' if success3 else '❌'}")
            
            # 测试4：增强的 save_visualization
            print("\n💾 测试增强的 save_visualization...")
            success4 = inference.save_visualization(
                results,
                "test_enhanced.glb",
                visualize_diffusion_steps=True,
                max_diffusion_steps=5
            )
            print(f"增强保存结果: {'✅' if success4 else '❌'}")
            
            # 测试5：尝试 GIF 生成（可能失败）
            print("\n🎬 测试 GIF 生成（可能需要显示环境）...")
            success5 = inference.save_diffusion_evolution(
                results,
                "test_evolution.gif",
                output_format="gif",
                max_steps=4
            )
            print(f"GIF 生成结果: {'✅' if success5 else '❌'}")
            if not success5:
                print("  注意：GIF 生成需要显示环境，在无头服务器上可能失败")
            
            # 总结
            total_success = sum([success1, success2, success3, success4])
            print(f"\n📈 测试总结:")
            print(f"  成功的测试: {total_success}/4 (不包括可选的GIF测试)")
            print(f"  GIF 测试: {'✅' if success5 else '❌'} (可选)")
            
            # 列出生成的文件
            print(f"\n📁 生成的文件:")
            test_files = [
                "test_diffusion_frames.glb",
                "test_diffusion_combined.glb", 
                "test_diffusion_evolution.gif",
                "test_frames_step_000.glb",
                "test_combined.glb",
                "test_enhanced.glb",
                "test_evolution.gif"
            ]
            
            for file_path in test_files:
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✓ {file_path} ({size} bytes)")
                    
            return total_success >= 3  # 至少3个测试成功
            
        else:
            print("❌ 没有捕获到中间步骤")
            print("   这可能是因为模型配置或推理设置问题")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖都已正确安装")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def cleanup_test_files():
    """清理测试生成的文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = [
        "test_diffusion_frames.glb",
        "test_diffusion_combined.glb", 
        "test_diffusion_evolution.gif",
        "test_combined.glb",
        "test_enhanced.glb",
        "test_evolution.gif"
    ]
    
    # 清理帧序列文件（可能有多个）
    for i in range(20):  # 最多检查20个帧文件
        frame_file = f"test_frames_step_{i:03d}.glb"
        if os.path.exists(frame_file):
            test_files.append(frame_file)
    
    cleaned_count = 0
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                cleaned_count += 1
                print(f"  ✓ 删除 {file_path}")
            except Exception as e:
                print(f"  ❌ 无法删除 {file_path}: {e}")
    
    print(f"✓ 清理了 {cleaned_count} 个测试文件")


if __name__ == "__main__":
    try:
        success = test_diffusion_visualization()
        
        if success:
            print("\n🎉 所有测试通过！")
            print("新的 diffusion 可视化功能工作正常。")
        else:
            print("\n⚠️ 部分测试失败")
            print("请检查错误信息并确保环境配置正确。")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        
    finally:
        # 询问是否清理测试文件
        try:
            response = input("\n是否清理测试生成的文件？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                cleanup_test_files()
            else:
                print("保留测试文件以供检查")
        except:
            print("保留测试文件")
