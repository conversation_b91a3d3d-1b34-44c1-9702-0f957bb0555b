"""
Simplified GraspLDM Inference Interface

This module provides a streamlined inference interface for GraspLDM that bypasses
the complex dataset loading pipeline and accepts raw point cloud data directly.

Key Features:
- Direct point cloud input (numpy arrays or torch tensors)
- Camera extrinsic parameter support
- Same preprocessing pipeline as original implementation
- Compatible with existing model weights and configurations
- Suitable for real-time robotic applications

Usage Example:
    Basic inference:
    >>> inference = SimpleGraspLDMInference(exp_path="path/to/experiment")
    >>> results = inference.infer_from_pointcloud(pointcloud_array, num_grasps=20)
    
    With visualization and saving:
    >>> results = inference.infer_from_pointcloud(
    ...     pointcloud=pointcloud_array,
    ...     num_grasps=20,
    ...     visualize=True,
    ...     save_path="grasps_visualization.glb"  # Supports .glb, .ply, .obj, .stl
    ... )

    # Generate grasps from point cloud
    results = inference.infer_from_pointcloud(
        pointcloud=pc_array,  # [N, 3] numpy array or torch tensor
        camera_pose=cam_pose, # [4, 4] camera extrinsic matrix (optional)
        num_grasps=20,
        visualize=True
    )
"""

import os
import warnings
from typing import Union, Optional, Tuple, Dict, Any

import numpy as np
import torch
import torch.nn as nn
import trimesh

from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config
from grasp_ldm.utils.rotations import tmrp_to_H
from grasp_ldm.utils.pointcloud_helpers import PointCloudHelpers
from grasp_ldm.utils.vis import visualize_pc_grasps
from tools.inference import Experiment, fix_state_dict_prefix, unnormalize_grasps, unnormalize_pc


class SimpleGraspLDMInference:
    """
    Simplified GraspLDM inference interface for direct point cloud input.

    This class provides a streamlined interface that bypasses the complex ACRONYM
    dataset structure and accepts raw point cloud data directly from cameras or
    other sources. It maintains full compatibility with existing model weights
    while offering a more flexible API for real-time applications.

    Key Capabilities:
    - Direct point cloud input (no dataset required)
    - Camera pose transformation support
    - Automatic point cloud preprocessing (centering, normalization)
    - Multiple conditioning modes (unconditional, class, region)
    - Built-in visualization support
    - Same output format as original inference pipeline

    Dimension Flow:
    - Input PC: [N, 3] raw coordinates → [1024, 3] regularized
    - Preprocessing: centering + normalization → [1024, 3] standardized
    - LDM Inference: [1024, 3] → [num_grasps, 4, 4] transformation matrices
    """

    def __init__(
        self,
        exp_path: str,                          # Path to experiment directory
        device: str = "cuda:0",                 # Compute device
        use_ema_model: bool = True,             # Use EMA model weights
        use_fast_sampler: bool = True,          # Enable fast sampling (DDIM)
        num_inference_steps: Optional[int] = 100, # Number of denoising steps
        num_points: int = 1024,                 # Target point cloud size
    ):
        """
        Initialize the simplified inference interface.

        Args:
            exp_path: Path to experiment directory containing model checkpoints
            device: PyTorch device for computation
            use_ema_model: Whether to use Exponential Moving Average weights
            use_fast_sampler: Enable fast sampling with DDIM scheduler
            num_inference_steps: Number of denoising steps for diffusion
            num_points: Target number of points for point cloud regularization
        """
        self.device = torch.device(device)
        self.use_ema_model = use_ema_model
        self.num_points = num_points

        # Initialize experiment handler
        self.experiment = Experiment(
            exp_name=os.path.basename(exp_path),
            exp_out_root=os.path.dirname(exp_path),
            modes=["ddm", "vae"]
        )

        # Load configuration
        self.config = self.experiment.get_config("ddm")

        # Setup diffusion sampler
        self._setup_ldm_sampler(num_inference_steps, use_fast_sampler)

        # Load model
        self.model = self._load_model()

        # Set normalization parameters (from dataset statistics)
        self._set_normalization_params()

        # Sigmoid for confidence scores
        self._sigmoid = nn.Sigmoid()

        print(f"✓ SimpleGraspLDMInference initialized")
        print(f"  Device: {self.device}")
        print(f"  Model: {'EMA' if use_ema_model else 'Standard'}")
        print(f"  Sampler: {'Fast DDIM' if use_fast_sampler else 'Standard DDPM'}")
        print(f"  Inference steps: {self.num_inference_steps}")

    def _setup_ldm_sampler(self, num_inference_steps: Optional[int], use_fast_sampler: bool):
        """Configure the diffusion sampler parameters."""
        if use_fast_sampler:
            self.config.models.ddm.model.args.noise_scheduler_type = "ddim"
            self.fast_sampler = "DDIM"
            self.num_inference_steps = 100 if num_inference_steps is None else num_inference_steps
        else:
            self.fast_sampler = None
            self.num_inference_steps = 1000 if num_inference_steps is None else num_inference_steps

    def _load_model(self):
        """Load the trained LDM model with VAE."""
        # Build model from configuration
        model = build_model_from_cfg(self.config.model.ddm)
        model.set_vae_model(build_model_from_cfg(self.config.model.vae))

        # Load checkpoint
        ckpt_path = self.experiment.get_ckpt_path("ddm")
        state_dict = torch.load(ckpt_path, map_location=self.device)["state_dict"]

        # Use appropriate model prefix (EMA vs standard)
        model_prefix = "model" if not self.use_ema_model else "ema_model.online_model"
        state_dict = fix_state_dict_prefix(state_dict, model_prefix, ignore_all_others=True)

        # Load weights
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=True)
        if missing_keys:
            warnings.warn(f"Missing keys while loading state dict: {missing_keys}")
        if unexpected_keys:
            warnings.warn(f"Found unexpected keys while loading state dict: {unexpected_keys}")

        return model.eval().to(self.device)

    def _set_normalization_params(self):
        """
        Set normalization parameters based on dataset statistics.

        These parameters are derived from the training data and must match
        exactly for proper model performance. The values are based on the
        AcronymPartialPointclouds dataset preprocessing pipeline.
        """
        # Point cloud normalization (zero-centered after per-object centering)
        self._INPUT_PC_SHIFT = torch.zeros((3,), device=self.device)
        self._INPUT_PC_SCALE = torch.ones((3,), device=self.device) * 0.05  # translation_scale

        # Grasp normalization (zero-centered after per-object centering)
        self._INPUT_GRASP_SHIFT = torch.zeros((6,), device=self.device)
        self._INPUT_GRASP_SCALE = torch.cat([
            torch.ones((3,), device=self.device) * 0.05,  # translation_scale
            torch.ones((3,), device=self.device) * 0.5,   # rotation_scale
        ])

        print(f"✓ Normalization parameters set:")
        print(f"  PC scale: {self._INPUT_PC_SCALE[0].item():.3f}")
        print(f"  Grasp translation scale: {self._INPUT_GRASP_SCALE[0].item():.3f}")
        print(f"  Grasp rotation scale: {self._INPUT_GRASP_SCALE[3].item():.3f}")

    def _prepare_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Prepare raw point cloud for inference.

        This method performs the same preprocessing steps as the original
        AcronymPartialPointclouds dataset:
        1. Convert to torch tensor
        2. Apply camera pose transformation (if provided)
        3. Regularize point count to target size
        4. Convert to float32

        Args:
            pointcloud: Raw point cloud [N, 3] in camera or world coordinates
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation

        Returns:
            torch.Tensor: Prepared point cloud [num_points, 3]
        """
        # Convert to torch tensor
        if isinstance(pointcloud, np.ndarray):
            pc = torch.from_numpy(pointcloud).float()
        else:
            pc = pointcloud.float()

        # Ensure correct shape
        if pc.ndim != 2 or pc.shape[1] != 3:
            raise ValueError(f"Point cloud must have shape [N, 3], got {pc.shape}")

        # Apply camera pose transformation if provided
        if camera_pose is not None:
            pc = self._transform_pointcloud(pc, camera_pose)

        # Regularize point count
        pc = self._regularize_pointcloud(pc, self.num_points)

        return pc.to(self.device)

    def _transform_pointcloud(
        self,
        pointcloud: torch.Tensor,
        camera_pose: Union[np.ndarray, torch.Tensor]
    ) -> torch.Tensor:
        """
        Transform point cloud using camera extrinsic parameters.

        Args:
            pointcloud: Point cloud in camera coordinates [N, 3]
            camera_pose: Camera extrinsic matrix [4, 4] (camera to world transform)

        Returns:
            torch.Tensor: Transformed point cloud [N, 3]
        """
        if isinstance(camera_pose, np.ndarray):
            camera_pose = torch.from_numpy(camera_pose).float()

        # Ensure correct shape
        if camera_pose.shape != (4, 4):
            raise ValueError(f"Camera pose must be [4, 4] matrix, got {camera_pose.shape}")

        # Convert to homogeneous coordinates
        ones = torch.ones(pointcloud.shape[0], 1, dtype=pointcloud.dtype, device=pointcloud.device)
        pc_homogeneous = torch.cat([pointcloud, ones], dim=1)  # [N, 4]

        # Apply transformation
        pc_transformed = torch.matmul(pc_homogeneous, camera_pose.T)  # [N, 4]

        # Return to 3D coordinates
        return pc_transformed[:, :3]

    def _regularize_pointcloud(self, pointcloud: torch.Tensor, target_points: int) -> torch.Tensor:
        """
        Regularize point cloud to have exactly target_points points.

        This matches the behavior of PointCloudHelpers.regularize_pc_point_count
        used in the original dataset pipeline.

        Args:
            pointcloud: Input point cloud [N, 3]
            target_points: Target number of points

        Returns:
            torch.Tensor: Regularized point cloud [target_points, 3]
        """
        current_points = pointcloud.shape[0]

        if current_points < target_points:
            # Upsample: repeat existing points
            multiplier = max(target_points // current_points, 1)
            pc = pointcloud.repeat(multiplier, 1)

            # Add random points to reach exact target
            num_extra = target_points - pc.shape[0]
            if num_extra > 0:
                extra_indices = torch.randperm(pc.shape[0])[:num_extra]
                extra_points = pc[extra_indices]
                pc = torch.cat([pc, extra_points], dim=0)

        elif current_points > target_points:
            # Downsample: random selection
            indices = torch.randperm(current_points)[:target_points]
            pc = pointcloud[indices]
        else:
            pc = pointcloud

        return pc

    def _preprocess_pointcloud(self, pointcloud: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Apply the same preprocessing pipeline as AcronymPartialPointclouds.

        This includes:
        1. Centering on point cloud mean
        2. Normalization using dataset statistics
        3. Metadata preparation for unnormalization

        Args:
            pointcloud: Prepared point cloud [num_points, 3]

        Returns:
            tuple: (preprocessed_pc, metadata_dict)
        """
        # Step 1: Center on point cloud mean
        pc_mean = torch.mean(pointcloud, dim=0)  # [3]
        pc_centered = pointcloud - pc_mean

        # Step 2: Normalize using dataset statistics
        pc_normalized = (pc_centered - self._INPUT_PC_SHIFT) / self._INPUT_PC_SCALE

        # Step 3: Prepare metadata for unnormalization
        grasp_mean = self._INPUT_GRASP_SHIFT.clone()
        grasp_mean[:3] += pc_mean  # Add centering offset to translation components

        metas = {
            "pc_mean": self._INPUT_PC_SHIFT + pc_mean,
            "pc_std": self._INPUT_PC_SCALE,
            "grasp_mean": grasp_mean,
            "grasp_std": self._INPUT_GRASP_SCALE,
            "dataset_normalized": True,
        }

        return pc_normalized, metas

    def _generate_grasps(
        self,
        pointcloud: torch.Tensor,
        metas: Dict[str, torch.Tensor],
        num_grasps: int = 20,
        return_intermediate: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Generate grasps using the LDM model.

        This method implements the core LDM inference pipeline:
        1. Point cloud encoding to latent space
        2. Diffusion sampling in grasp latent space
        3. VAE decoding to grasp poses
        4. Post-processing and unnormalization

        Args:
            pointcloud: Preprocessed point cloud [num_points, 3]
            metas: Metadata dictionary for unnormalization
            num_grasps: Number of grasps to generate
            return_intermediate: Whether to return intermediate diffusion steps

        Returns:
            dict: Generated results containing:
                - grasps: [num_grasps, 4, 4] homogeneous transformation matrices
                - confidence: [num_grasps, 1] success probabilities
                - pc: [num_points, 3] unnormalized point cloud
                - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
        """
        # Ensure batch dimension
        batch_pc = pointcloud.unsqueeze(0)  # [1, num_points, 3]

        # Move metadata to device
        metas = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                for k, v in metas.items()}

        # Prepare model input
        in_kwargs = {"xyz": batch_pc, "metas": metas}

        # Configure fast sampling if enabled
        if self.fast_sampler == "DDIM":
            self.model.set_inference_timesteps(self.num_inference_steps)

        # Execute LDM inference with intermediate step control
        with torch.no_grad():
            final_grasps, all_diffusion_grasps = self.model.generate_grasps(
                num_grasps=num_grasps, 
                return_intermediate=return_intermediate, 
                **in_kwargs
            )

        # Parse model outputs
        if self.model.vae_model.decoder._use_qualities:
            tmrp, cls_logit, qualities = final_grasps
        else:
            tmrp, cls_logit = final_grasps
            qualities = None

        # Reshape to proper dimensions
        tmrp = tmrp.view(1, num_grasps, tmrp.shape[-1])

        # Unnormalize grasp poses
        grasp_unnorm = unnormalize_grasps(tmrp, metas)

        # Convert to homogeneous transformation matrices
        H_grasps = tmrp_to_H(grasp_unnorm)  # [1, num_grasps, 4, 4]

        # Process intermediate diffusion steps if available
        all_steps_grasps = []
        if return_intermediate and all_diffusion_grasps:
            # Note: For batched inference (batch_size > 1), intermediate steps are not supported
            # This follows the same limitation as InferenceLDM
            if batch_pc.shape[0] > 1:
                print("⚠️  Warning: Batched inference with intermediate steps not supported. Skipping intermediate results.")
            else:
                for step_grasp in all_diffusion_grasps:
                    # Each step_grasp is (tmrp, cls_logit) or (tmrp, cls_logit, qualities)
                    step_tmrp = step_grasp[0]
                    step_grasp_unnorm = unnormalize_grasps(step_tmrp, metas)
                    step_H_grasps = tmrp_to_H(step_grasp_unnorm)
                    all_steps_grasps.append(step_H_grasps.squeeze(0))  # Remove batch dimension

        # Compute confidence scores
        confidence = cls_logit.view(1, num_grasps, cls_logit.shape[-1])
        confidence = self._sigmoid(confidence)

        # Unnormalize point cloud
        pc_unnorm = unnormalize_pc(batch_pc, metas)

        return {
            "grasps": H_grasps.squeeze(0),      # [num_grasps, 4, 4]
            "grasp_tmrp": grasp_unnorm.squeeze(0),  # [num_grasps, 6] translation + rotation MRP
            "confidence": confidence.squeeze(0), # [num_grasps, 1]
            "pc": pc_unnorm.squeeze(0),         # [num_points, 3]
            "qualities": qualities,              # Optional quality metrics
            "all_steps_grasps": all_steps_grasps, # List of intermediate diffusion steps
        }

    def infer_from_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None,
        num_grasps: int = 20,
        return_intermediate: bool = False,
        visualize: bool = False,
        return_scene: bool = False,
        save_path: str = None
    ) -> Union[Dict[str, torch.Tensor], Any]:
        """
        Main inference method for generating grasps from raw point cloud data.

        This is the primary interface for the simplified inference pipeline.
        It accepts raw point cloud data and optional camera parameters, then
        performs the complete preprocessing and inference pipeline.

        Args:
            pointcloud: Raw point cloud [N, 3] as numpy array or torch tensor
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation
            num_grasps: Number of grasps to generate (default: 20)
            return_intermediate: Whether to return intermediate diffusion steps (default: False)
            visualize: Whether to display 3D visualization (default: False)
            return_scene: If True and visualize=True, return scene object instead of showing
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)

        Returns:
            dict or Scene: If visualize=False, returns dict with:
                          - grasps: [num_grasps, 4, 4] transformation matrices
                          - grasp_tmrp: [num_grasps, 6] translation + rotation MRP
                          - confidence: [num_grasps, 1] success probabilities
                          - pc: [num_points, 3] processed point cloud
                          - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
                          If visualize=True and return_scene=True, returns 3D scene object
                          If visualize=True and return_scene=False, shows visualization and returns dict

        Example:
            >>> # Basic usage
            >>> results = inference.infer_from_pointcloud(pc_array, num_grasps=10)
            >>> grasps = results["grasps"]  # [10, 4, 4] transformation matrices
            >>> confidence = results["confidence"]  # [10, 1] success probabilities

            >>> # With intermediate steps for analysis
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=20,
            ...     return_intermediate=True
            ... )
            >>> intermediate_steps = results["all_steps_grasps"]  # List of intermediate results

            >>> # With camera pose transformation and saving
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_camera_coords,
            ...     camera_pose=camera_to_world_matrix,
            ...     num_grasps=20,
            ...     visualize=True,
            ...     save_path="grasps.glb"  # or .ply, .obj, .stl
            ... )
        """
        print(f"🚀 Starting grasp inference...")
        print(f"  Input PC shape: {pointcloud.shape}")
        print(f"  Target grasps: {num_grasps}")
        print(f"  Camera pose: {'Yes' if camera_pose is not None else 'No'}")
        print(f"  Return intermediate: {'Yes' if return_intermediate else 'No'}")

        # Step 1: Prepare point cloud
        pc_prepared = self._prepare_pointcloud(pointcloud, camera_pose)
        print(f"  ✓ Point cloud prepared: {pc_prepared.shape}")

        # Step 2: Preprocess (center + normalize)
        pc_preprocessed, metas = self._preprocess_pointcloud(pc_prepared)
        print(f"  ✓ Preprocessing complete")

        # Step 3: Generate grasps with intermediate step support
        results = self._generate_grasps(pc_preprocessed, metas, num_grasps, return_intermediate)
        print(f"  ✓ Generated {results['grasps'].shape[0]} grasps")
        if return_intermediate and results.get('all_steps_grasps'):
            print(f"  ✓ Captured {len(results['all_steps_grasps'])} intermediate steps")

        # Step 4: Visualization (if requested)
        if visualize:
            scene = self._visualize_results(results, return_scene, save_path)
            if return_scene:
                return scene
            # If not returning scene, show it and continue to return results

        print(f"✅ Inference complete!")
        return results

    def _visualize_results(self, results: Dict[str, torch.Tensor], return_scene: bool = False, save_path: str = None):
        """
        Visualize generated grasps in 3D with optional saving.

        Args:
            results: Results dictionary from inference
            return_scene: Whether to return scene object or show directly
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)

        Returns:
            Scene object if return_scene=True, otherwise None
        """
        # Convert to numpy for visualization
        pc_np = results["pc"].detach().cpu().numpy()
        grasps_np = results["grasps"].detach().cpu().numpy()
        confidence_np = results["confidence"].detach().cpu().numpy()

        # Create 3D scene
        scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)

        # Save scene if path provided
        if save_path is not None:
            try:
                scene.export(save_path)
                print(f"✅ Visualization saved to: {save_path}")
            except Exception as e:
                print(f"❌ Failed to save visualization: {e}")

        if return_scene:
            return scene
        else:
            scene.show(line_settings={"point_size": 10})
            return None

    def load_pointcloud_from_file(self, filepath: str) -> np.ndarray:
        """
        Load point cloud from common file formats.

        Supports .ply, .pcd, .xyz, and .txt files.

        Args:
            filepath: Path to point cloud file

        Returns:
            np.ndarray: Point cloud [N, 3]
        """
        import trimesh

        if filepath.endswith('.ply'):
            mesh = trimesh.load(filepath)
            if hasattr(mesh, 'vertices'):
                return np.array(mesh.vertices)
            else:
                raise ValueError("PLY file does not contain vertices")
        elif filepath.endswith('.pcd'):
            # Basic PCD file support
            import open3d as o3d
            pcd = o3d.io.read_point_cloud(filepath)
            return np.asarray(pcd.points)
        elif filepath.endswith(('.xyz', '.txt')):
            # Simple text format: x y z per line
            return np.loadtxt(filepath)[:, :3]
        else:
            raise ValueError(f"Unsupported file format: {filepath}")

    def filter_grasps_by_confidence(
        self,
        results: Dict[str, torch.Tensor],
        min_confidence: float = 0.5
    ) -> Dict[str, torch.Tensor]:
        """
        Filter generated grasps by confidence threshold.

        Args:
            results: Results dictionary from inference
            min_confidence: Minimum confidence threshold (0.0 to 1.0)

        Returns:
            dict: Filtered results with same structure
        """
        confidence = results["confidence"].squeeze(-1)  # [num_grasps]
        mask = confidence >= min_confidence

        if mask.sum() == 0:
            print(f"⚠️  No grasps above confidence threshold {min_confidence:.2f}")
            return results

        filtered_results = {
            "grasps": results["grasps"][mask],
            "grasp_tmrp": results["grasp_tmrp"][mask],
            "confidence": results["confidence"][mask],
            "pc": results["pc"],  # Point cloud unchanged
        }

        if "qualities" in results and results["qualities"] is not None:
            filtered_results["qualities"] = results["qualities"][mask]

        print(f"✓ Filtered {mask.sum().item()}/{len(mask)} grasps above confidence {min_confidence:.2f}")
        return filtered_results

    def get_best_grasps(
        self,
        results: Dict[str, torch.Tensor],
        top_k: int = 5
    ) -> Dict[str, torch.Tensor]:
        """
        Get top-k grasps by confidence score.

        Args:
            results: Results dictionary from inference
            top_k: Number of top grasps to return

        Returns:
            dict: Top-k results with same structure
        """
        confidence = results["confidence"].squeeze(-1)  # [num_grasps]
        _, top_indices = torch.topk(confidence, min(top_k, len(confidence)))

        top_results = {
            "grasps": results["grasps"][top_indices],
            "grasp_tmrp": results["grasp_tmrp"][top_indices],
            "confidence": results["confidence"][top_indices],
            "pc": results["pc"],  # Point cloud unchanged
        }

        if "qualities" in results and results["qualities"] is not None:
            top_results["qualities"] = results["qualities"][top_indices]

        print(f"✓ Selected top-{len(top_indices)} grasps")
        return top_results

    def visualize_diffusion_process(self, results: Dict[str, torch.Tensor],
                                   max_steps: int = 10,
                                   save_base_path: str = None) -> bool:
        """
        Convenient method to visualize the entire diffusion process evolution.

        This method provides a one-stop solution for visualizing how grasps evolve
        during the diffusion denoising process. It automatically creates multiple
        visualization formats if intermediate steps are available.

        Args:
            results: Results dictionary containing 'all_steps_grasps'
            max_steps: Maximum number of diffusion steps to visualize
            save_base_path: Base path for saving visualizations (optional)
                          If provided, saves frames, combined scene, and attempts GIF

        Returns:
            bool: True if any visualization was successful

        Example:
            >>> # Generate grasps with intermediate steps
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=10,
            ...     return_intermediate=True
            ... )
            >>>
            >>> # Visualize the diffusion process
            >>> inference.visualize_diffusion_process(
            ...     results,
            ...     max_steps=8,
            ...     save_base_path="my_diffusion_viz"
            ... )
        """
        all_steps_grasps = results.get('all_steps_grasps', [])
        if not all_steps_grasps:
            print("⚠️  No intermediate diffusion steps available for visualization")
            print("   Use return_intermediate=True when calling infer_from_pointcloud()")
            return False

        print(f"🎬 Visualizing diffusion process evolution...")
        print(f"  Available steps: {len(all_steps_grasps)}")
        print(f"  Max steps to show: {max_steps}")

        success_count = 0

        if save_base_path:
            # Save multiple formats
            formats = [
                ("frames", f"{save_base_path}_frames.glb"),
                ("combined", f"{save_base_path}_combined.glb"),
                ("gif", f"{save_base_path}_evolution.gif")
            ]

            for format_name, path in formats:
                print(f"  Creating {format_name} visualization...")
                success = self.save_diffusion_evolution(
                    results, path, output_format=format_name, max_steps=max_steps
                )
                if success:
                    success_count += 1
                    print(f"    ✅ {format_name.capitalize()} saved successfully")
                else:
                    print(f"    ❌ {format_name.capitalize()} failed")
        else:
            # Just show information about available steps
            print("  Diffusion step analysis:")
            pc_np = results["pc"].detach().cpu().numpy()

            # Sample a few steps to show statistics
            sample_indices = np.linspace(0, len(all_steps_grasps) - 1,
                                       min(5, len(all_steps_grasps)), dtype=int)

            for i, step_idx in enumerate(sample_indices):
                step_grasps = all_steps_grasps[step_idx].detach().cpu().numpy()
                print(f"    Step {step_idx:3d}: {step_grasps.shape[0]} grasps")

            print(f"  💡 To save visualizations, provide save_base_path parameter")
            success_count = 1  # Consider info display as success

        print(f"✅ Diffusion visualization complete ({success_count} operations successful)")
        return success_count > 0

    def save_visualization(self, results: Dict[str, torch.Tensor], save_path: str,
                         include_confidence_colors: bool = True,
                         visualize_diffusion_steps: bool = False,
                         max_diffusion_steps: int = 10) -> bool:
        """
        Save visualization of grasps to file with enhanced options including diffusion process.

        Args:
            results: Results dictionary from inference
            save_path: Path to save the visualization
            include_confidence_colors: Whether to color grasps by confidence
            visualize_diffusion_steps: Whether to create diffusion evolution visualization
            max_diffusion_steps: Maximum number of diffusion steps to visualize

        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Convert to numpy for visualization
            pc_np = results["pc"].detach().cpu().numpy()
            grasps_np = results["grasps"].detach().cpu().numpy()
            confidence_np = results["confidence"].detach().cpu().numpy() if include_confidence_colors else None

            # Check if we should visualize diffusion steps
            if visualize_diffusion_steps and results.get('all_steps_grasps'):
                return self.save_diffusion_evolution(
                    results, save_path, include_confidence_colors, max_diffusion_steps
                )

            # Create standard 3D scene for final results
            scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)

            # Ensure directory exists
            import os
            dir_path = os.path.dirname(save_path)
            if dir_path:  # Only create directory if path contains a directory
                os.makedirs(dir_path, exist_ok=True)

            # Export scene
            scene.export(save_path)

            # Validate file was created
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Visualization saved to: {save_path} ({file_size} bytes)")
                return True
            else:
                print(f"❌ File was not created: {save_path}")
                return False

        except Exception as e:
            print(f"❌ Failed to save visualization: {e}")
            import traceback
            traceback.print_exc()
            return False

    def save_diffusion_evolution(self, results: Dict[str, torch.Tensor], save_path: str,
                                include_confidence_colors: bool = True,
                                max_steps: int = 10,
                                output_format: str = "frames") -> bool:
        """
        Save visualization of the entire diffusion process evolution.

        This function creates visualizations showing how grasps evolve during the
        diffusion denoising process, providing insights into the generation dynamics.

        Args:
            results: Results dictionary containing 'all_steps_grasps'
            save_path: Base path for saving (extension determines format)
            include_confidence_colors: Whether to use confidence-based coloring
            max_steps: Maximum number of diffusion steps to visualize
            output_format: Output format - "frames", "gif", or "combined"
                - "frames": Save individual frame files
                - "gif": Create animated GIF (requires imageio)
                - "combined": Save multi-step combined scene

        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            all_steps_grasps = results.get('all_steps_grasps', [])
            if not all_steps_grasps:
                print("⚠️  No intermediate diffusion steps available for visualization")
                return False

            print(f"🎬 Creating diffusion evolution visualization...")
            print(f"  Total steps available: {len(all_steps_grasps)}")
            print(f"  Max steps to visualize: {max_steps}")
            print(f"  Output format: {output_format}")

            # Convert point cloud to numpy
            pc_np = results["pc"].detach().cpu().numpy()

            # Sample steps to visualize (evenly distributed)
            total_steps = len(all_steps_grasps)
            if total_steps > max_steps:
                step_indices = np.linspace(0, total_steps - 1, max_steps, dtype=int)
            else:
                step_indices = list(range(total_steps))

            print(f"  Selected step indices: {step_indices}")

            # Prepare base path and directory
            import os
            base_path = os.path.splitext(save_path)[0]
            dir_path = os.path.dirname(save_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            if output_format == "frames":
                return self._save_diffusion_frames(
                    pc_np, all_steps_grasps, step_indices, base_path, include_confidence_colors
                )
            elif output_format == "gif":
                return self._save_diffusion_gif(
                    pc_np, all_steps_grasps, step_indices, save_path, include_confidence_colors
                )
            elif output_format == "combined":
                return self._save_diffusion_combined(
                    pc_np, all_steps_grasps, step_indices, save_path, include_confidence_colors
                )
            else:
                print(f"❌ Unsupported output format: {output_format}")
                return False

        except Exception as e:
            print(f"❌ Failed to save diffusion evolution: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _save_diffusion_frames(self, pc_np: np.ndarray, all_steps_grasps: list,
                              step_indices: list, base_path: str,
                              include_confidence_colors: bool = True) -> bool:
        """
        Save individual frames for each diffusion step.

        Args:
            pc_np: Point cloud as numpy array [N, 3]
            all_steps_grasps: List of grasp tensors for each diffusion step
            step_indices: Indices of steps to visualize
            base_path: Base path for saving frames
            include_confidence_colors: Whether to use confidence coloring

        Returns:
            bool: True if successful
        """
        try:
            saved_files = []

            for i, step_idx in enumerate(step_indices):
                # Convert step grasps to numpy
                step_grasps = all_steps_grasps[step_idx].detach().cpu().numpy()

                # Create confidence colors based on step progress (early steps = red, late steps = green)
                if include_confidence_colors:
                    # Color based on diffusion progress: red (early) -> yellow -> green (late)
                    progress = i / (len(step_indices) - 1) if len(step_indices) > 1 else 1.0
                    confidence_colors = np.full(step_grasps.shape[0], progress)
                else:
                    confidence_colors = None

                # Create scene for this step
                scene = visualize_pc_grasps(pc_np, step_grasps, confidence_colors)

                # Save frame
                frame_path = f"{base_path}_step_{step_idx:03d}.glb"
                scene.export(frame_path)
                saved_files.append(frame_path)

                print(f"  ✓ Saved frame {i+1}/{len(step_indices)}: {frame_path}")

            print(f"✅ Saved {len(saved_files)} diffusion frames")
            return True

        except Exception as e:
            print(f"❌ Failed to save diffusion frames: {e}")
            return False

    def _save_diffusion_gif(self, pc_np: np.ndarray, all_steps_grasps: list,
                           step_indices: list, save_path: str,
                           include_confidence_colors: bool = True) -> bool:
        """
        Save animated GIF of diffusion evolution.

        Args:
            pc_np: Point cloud as numpy array [N, 3]
            all_steps_grasps: List of grasp tensors for each diffusion step
            step_indices: Indices of steps to visualize
            save_path: Path to save GIF
            include_confidence_colors: Whether to use confidence coloring

        Returns:
            bool: True if successful
        """
        try:
            # Try to import required libraries
            try:
                import imageio
                from PIL import Image
                import tempfile
            except ImportError:
                print("❌ GIF creation requires 'imageio' and 'PIL'. Install with: pip install imageio pillow")
                return False

            # Create temporary directory for frames
            with tempfile.TemporaryDirectory() as temp_dir:
                frame_paths = []

                for i, step_idx in enumerate(step_indices):
                    # Convert step grasps to numpy
                    step_grasps = all_steps_grasps[step_idx].detach().cpu().numpy()

                    # Create confidence colors based on step progress
                    if include_confidence_colors:
                        progress = i / (len(step_indices) - 1) if len(step_indices) > 1 else 1.0
                        confidence_colors = np.full(step_grasps.shape[0], progress)
                    else:
                        confidence_colors = None

                    # Create scene and render to image
                    scene = visualize_pc_grasps(pc_np, step_grasps, confidence_colors)

                    # Save as PNG for GIF creation
                    frame_path = os.path.join(temp_dir, f"frame_{i:03d}.png")

                    # Use trimesh's built-in screenshot capability
                    # Note: This requires a display or virtual display
                    try:
                        scene.save_image(frame_path, resolution=(800, 600))
                        frame_paths.append(frame_path)
                    except Exception as render_error:
                        print(f"⚠️  Could not render frame {i}: {render_error}")
                        print("   GIF creation requires display capability or virtual display")
                        return False

                if not frame_paths:
                    print("❌ No frames were successfully rendered")
                    return False

                # Create GIF from frames
                images = []
                for frame_path in frame_paths:
                    images.append(imageio.imread(frame_path))

                # Save GIF with appropriate duration
                gif_path = os.path.splitext(save_path)[0] + ".gif"
                imageio.mimsave(gif_path, images, duration=0.5, loop=0)

                print(f"✅ Saved diffusion evolution GIF: {gif_path}")
                return True

        except Exception as e:
            print(f"❌ Failed to create diffusion GIF: {e}")
            return False

    def _save_diffusion_combined(self, pc_np: np.ndarray, all_steps_grasps: list,
                                step_indices: list, save_path: str,
                                include_confidence_colors: bool = True) -> bool:
        """
        Save combined scene with all diffusion steps in one visualization.

        Args:
            pc_np: Point cloud as numpy array [N, 3]
            all_steps_grasps: List of grasp tensors for each diffusion step
            step_indices: Indices of steps to visualize
            save_path: Path to save combined scene
            include_confidence_colors: Whether to use confidence coloring

        Returns:
            bool: True if successful
        """
        try:
            # Create combined scene with point cloud
            r = pc_np[..., 0] * 255 / max(pc_np[..., 0])
            g = pc_np[..., 1] * 200 / max(pc_np[..., 1])
            b = pc_np[..., 2] * 175 / max(pc_np[..., 2])
            a = np.ones(pc_np.shape[0]) * 200
            pc_colors = np.clip(np.vstack((r, g, b, a)).T, 0, 255)

            scene_objects = [trimesh.points.PointCloud(pc_np, colors=pc_colors)]

            # Add grasps from each step with different colors/transparency
            for i, step_idx in enumerate(step_indices):
                step_grasps = all_steps_grasps[step_idx].detach().cpu().numpy()

                # Create color based on step progress and transparency
                if include_confidence_colors:
                    progress = i / (len(step_indices) - 1) if len(step_indices) > 1 else 1.0
                    # Early steps: more transparent red, late steps: opaque green
                    alpha = int(50 + 205 * progress)  # 50-255 alpha range
                    red = int(255 * (1 - progress))
                    green = int(255 * progress)
                    color = [red, green, 0, alpha]
                else:
                    # Use different hues for each step
                    hue = i / len(step_indices) * 360
                    import colorsys
                    rgb = colorsys.hsv_to_rgb(hue/360, 1.0, 1.0)
                    color = [int(255*rgb[0]), int(255*rgb[1]), int(255*rgb[2]), 150]

                # Create gripper markers for this step
                from grasp_ldm.utils.vis import create_gripper_marker
                gripper_markers = []
                for grasp in step_grasps:
                    marker = create_gripper_marker(color=color)
                    marker = marker.copy().apply_transform(grasp)
                    gripper_markers.append(marker)

                scene_objects.extend(gripper_markers)

            # Create and save combined scene
            combined_scene = trimesh.Scene(scene_objects)
            combined_scene.export(save_path)

            print(f"✅ Saved combined diffusion visualization: {save_path}")
            print(f"   Contains {len(step_indices)} diffusion steps")
            return True

        except Exception as e:
            print(f"❌ Failed to save combined diffusion scene: {e}")
            return False


def demo_simple_inference():
    """
    Demonstration of the simplified inference interface.

    This function shows how to use the SimpleGraspLDMInference class
    with synthetic data to generate grasps.
    """
    print("🎯 GraspLDM Simplified Inference Demo")
    print("=" * 50)

    # Initialize inference engine
    exp_path = "checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k"

    if not os.path.exists(exp_path):
        print(f"❌ Experiment path not found: {exp_path}")
        print("Please ensure you have downloaded the model checkpoints.")
        return

    try:
        inference = SimpleGraspLDMInference(
            exp_path=exp_path,
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            num_inference_steps=50  # Faster for demo
        )

        # Generate synthetic point cloud (sphere)
        print("\n📊 Generating synthetic point cloud...")
        theta = np.random.uniform(0, 2*np.pi, 2000)
        phi = np.random.uniform(0, np.pi, 2000)
        r = 0.18  # 18cm radius sphere

        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)

        synthetic_pc = np.column_stack([x, y, z])
        print(f"✓ Created sphere point cloud: {synthetic_pc.shape}")

        # Generate grasps
        print("\n🤖 Generating grasps...")
        results = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=False  # Set to True to see 3D visualization
        )

        # Demonstrate intermediate steps feature
        print("\n🔍 Generating grasps with intermediate steps...")
        results_with_steps = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=10,
            return_intermediate=True,
            visualize=False
        )

        # Demonstrate saving visualization
        print("\n💾 Saving visualization...")
        results_with_viz = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=True,
            save_path="demo_grasps.glb"  # Save as GLB format
        )

        # Demonstrate diffusion evolution visualization
        print("\n🎬 Demonstrating diffusion evolution visualization...")
        if results_with_steps.get('all_steps_grasps'):
            # Save individual frames
            print("  Saving diffusion frames...")
            success_frames = inference.save_diffusion_evolution(
                results_with_steps,
                "demo_diffusion_frames.glb",
                output_format="frames",
                max_steps=5
            )

            # Save combined visualization
            print("  Saving combined diffusion scene...")
            success_combined = inference.save_diffusion_evolution(
                results_with_steps,
                "demo_diffusion_combined.glb",
                output_format="combined",
                max_steps=8
            )

            # Try to save GIF (may fail without display)
            print("  Attempting to save diffusion GIF...")
            success_gif = inference.save_diffusion_evolution(
                results_with_steps,
                "demo_diffusion_evolution.gif",
                output_format="gif",
                max_steps=6
            )

            print(f"  Diffusion visualization results:")
            print(f"    Frames: {'✅' if success_frames else '❌'}")
            print(f"    Combined: {'✅' if success_combined else '❌'}")
            print(f"    GIF: {'✅' if success_gif else '❌'}")

            # Demonstrate the convenient visualization method
            print("\n🎯 Using convenient diffusion visualization method...")
            inference.visualize_diffusion_process(
                results_with_steps,
                max_steps=6,
                save_base_path="demo_convenient_diffusion"
            )
        else:
            print("  ⚠️  No intermediate steps available for diffusion visualization")
            print("     Make sure to use return_intermediate=True in inference")

        # Analyze results
        print(f"\n📈 Results Analysis:")
        print(f"  Generated grasps: {results['grasps'].shape[0]}")
        print(f"  Confidence range: {results['confidence'].min():.3f} - {results['confidence'].max():.3f}")
        print(f"  Mean confidence: {results['confidence'].mean():.3f}")
        
        # Analyze intermediate steps
        if results_with_steps.get('all_steps_grasps'):
            print(f"  Intermediate steps captured: {len(results_with_steps['all_steps_grasps'])}")
            print(f"  Each step shape: {results_with_steps['all_steps_grasps'][0].shape}")
        else:
            print(f"  No intermediate steps captured (may require return_intermediate=True)")

        # Filter high-confidence grasps
        good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.7)

        # Get top grasps
        top_grasps = inference.get_best_grasps(results, top_k=5)

        print(f"\n✅ Demo completed successfully!")
        print(f"  High-confidence grasps: {good_grasps['grasps'].shape[0]}")
        print(f"  Top-5 grasps confidence: {top_grasps['confidence'].squeeze().tolist()}")

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    demo_simple_inference()
